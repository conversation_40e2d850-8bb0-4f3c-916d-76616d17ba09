{"name": "handcrafted_haven", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed:categories": "node scripts/seed-categories.js"}, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@next-auth/mongodb-adapter": "^1.1.3", "bcryptjs": "^3.0.2", "dotenv": "^17.2.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "multer": "^2.0.2", "next": "15.4.4", "next-auth": "^4.24.11", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "^0.34.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.4", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}